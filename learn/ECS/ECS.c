#include "ECS.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// Entity functions
const char* entity_get_tag(const Entity* entity) {
    if (!entity) return NULL;
    return entity->tag;
}

size_t entity_get_id(const Entity* entity) {
    if (!entity) return 0;
    return entity->id;
}

void entity_destroy(Entity* entity) {
    if (!entity) return;
    entity->alive = false;
}

// Helper function to get a free component from pool
static size_t get_free_component_index(size_t** free_indices, void** pool, size_t component_size) {
    if (arrlen(*free_indices) > 0) {
        // Use a free index
        size_t index = arrpop(*free_indices);
        return index;
    }

    // No free indices, expand the pool
    size_t new_index = arrlen(*pool);

    // Expand the pool by one element
    *pool = stbds_arrgrowf(*pool, component_size, 1, 0);
    stbds_header(*pool)->length++;

    // Zero out the new component
    memset((char*)*pool + new_index * component_size, 0, component_size);

    return new_index;
}

// Helper function to return a component to the free pool
static void return_component_index(size_t** free_indices, size_t index) {
    arrput(*free_indices, index);
}

// Component management functions
void entity_add_transform(Entity* entity, float x, float y, float angle, float scale_x, float scale_y) {
    if (!entity || !entity->manager) return;

    if (entity->c_transform) {
        // Update existing transform
        entity->c_transform->x = x;
        entity->c_transform->y = y;
        entity->c_transform->angle = angle;
        entity->c_transform->scale_x = scale_x;
        entity->c_transform->scale_y = scale_y;
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(&manager->transform_free_indices,
                                          (void**)&manager->transform_pool,
                                          sizeof(CTransform));

    CTransform* transform = &manager->transform_pool[index];
    transform->x = x;
    transform->y = y;
    transform->angle = angle;
    transform->scale_x = scale_x;
    transform->scale_y = scale_y;

    entity->c_transform = transform;
}

void entity_add_name(Entity* entity, const char* name) {
    if (!entity || !entity->manager || !name) return;

    if (entity->c_name) {
        // Update existing name
        strncpy(entity->c_name->name, name, sizeof(entity->c_name->name) - 1);
        entity->c_name->name[sizeof(entity->c_name->name) - 1] = '\0';
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(&manager->name_free_indices,
                                          (void**)&manager->name_pool,
                                          sizeof(CName));

    CName* name_comp = &manager->name_pool[index];
    strncpy(name_comp->name, name, sizeof(name_comp->name) - 1);
    name_comp->name[sizeof(name_comp->name) - 1] = '\0';

    entity->c_name = name_comp;
}

void entity_add_shape(Entity* entity, const char* type) {
    if (!entity || !entity->manager || !type) return;

    if (entity->c_shape) {
        // Update existing shape
        strncpy(entity->c_shape->type, type, sizeof(entity->c_shape->type) - 1);
        entity->c_shape->type[sizeof(entity->c_shape->type) - 1] = '\0';
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(&manager->shape_free_indices,
                                          (void**)&manager->shape_pool,
                                          sizeof(CShape));

    CShape* shape = &manager->shape_pool[index];
    strncpy(shape->type, type, sizeof(shape->type) - 1);
    shape->type[sizeof(shape->type) - 1] = '\0';

    entity->c_shape = shape;
}

void entity_add_bounding_box(Entity* entity, float x, float y, float width, float height) {
    if (!entity || !entity->manager) return;

    if (entity->c_bounding_box) {
        // Update existing bounding box
        entity->c_bounding_box->x = x;
        entity->c_bounding_box->y = y;
        entity->c_bounding_box->width = width;
        entity->c_bounding_box->height = height;
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(&manager->bounding_box_free_indices,
                                          (void**)&manager->bounding_box_pool,
                                          sizeof(CBoundingBox));

    CBoundingBox* bounding_box = &manager->bounding_box_pool[index];
    bounding_box->x = x;
    bounding_box->y = y;
    bounding_box->width = width;
    bounding_box->height = height;

    entity->c_bounding_box = bounding_box;
}



void entity_add_text(Entity* entity, const char* text, int font_size, float r, float g, float b, float a) {
    if (!entity || !entity->manager || !text) return;

    if (entity->c_text) {
        // Update existing text
        strncpy(entity->c_text->text, text, sizeof(entity->c_text->text) - 1);
        entity->c_text->text[sizeof(entity->c_text->text) - 1] = '\0';
        entity->c_text->font_size = font_size;
        entity->c_text->r = r;
        entity->c_text->g = g;
        entity->c_text->b = b;
        entity->c_text->a = a;
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(&manager->text_free_indices,
                                          (void**)&manager->text_pool,
                                          sizeof(CText));

    CText* text_comp = &manager->text_pool[index];
    strncpy(text_comp->text, text, sizeof(text_comp->text) - 1);
    text_comp->text[sizeof(text_comp->text) - 1] = '\0';
    text_comp->font_size = font_size;
    text_comp->r = r;
    text_comp->g = g;
    text_comp->b = b;
    text_comp->a = a;

    entity->c_text = text_comp;
}

// Component removal functions
void entity_remove_transform(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_transform) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_transform - manager->transform_pool;
    return_component_index(&manager->transform_free_indices, index);
    entity->c_transform = NULL;
}

void entity_remove_name(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_name) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_name - manager->name_pool;
    return_component_index(&manager->name_free_indices, index);
    entity->c_name = NULL;
}

void entity_remove_shape(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_shape) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_shape - manager->shape_pool;
    return_component_index(&manager->shape_free_indices, index);
    entity->c_shape = NULL;
}

void entity_remove_bounding_box(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_bounding_box) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_bounding_box - manager->bounding_box_pool;
    return_component_index(&manager->bounding_box_free_indices, index);
    entity->c_bounding_box = NULL;
}

void entity_remove_text(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_text) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_text - manager->text_pool;
    return_component_index(&manager->text_free_indices, index);
    entity->c_text = NULL;
}

// Component check functions
bool entity_has_transform(const Entity* entity) {
    return entity && entity->c_transform != NULL;
}

bool entity_has_name(const Entity* entity) {
    return entity && entity->c_name != NULL;
}

bool entity_has_shape(const Entity* entity) {
    return entity && entity->c_shape != NULL;
}

bool entity_has_bounding_box(const Entity* entity) {
    return entity && entity->c_bounding_box != NULL;
}



bool entity_has_text(const Entity* entity) {
    return entity && entity->c_text != NULL;
}

// EntityManager functions
EntityManager* entity_manager_create(void) {
    EntityManager* manager = malloc(sizeof(EntityManager));
    if (!manager) return NULL;

    // Initialize dynamic arrays to NULL (stb_ds requirement)
    manager->entities = NULL;
    manager->entity_map = NULL;
    manager->entities_to_add = NULL;
    manager->next_id = 1; // Start IDs from 1, 0 is reserved for invalid

    // Initialize dynamic component pools to NULL
    manager->transform_pool = NULL;
    manager->name_pool = NULL;
    manager->shape_pool = NULL;
    manager->bounding_box_pool = NULL;
    manager->text_pool = NULL;

    // Initialize dynamic free indices arrays to NULL
    manager->transform_free_indices = NULL;
    manager->name_free_indices = NULL;
    manager->shape_free_indices = NULL;
    manager->bounding_box_free_indices = NULL;
    manager->text_free_indices = NULL;

    return manager;
}

void entity_manager_destroy(EntityManager* manager) {
    if (!manager) return;

    // Clean up all entities (remove their components)
    for (size_t i = 0; i < arrlen(manager->entities); i++) {
        Entity* entity = &manager->entities[i];
        entity_remove_transform(entity);
        entity_remove_name(entity);
        entity_remove_shape(entity);
        entity_remove_bounding_box(entity);
        entity_remove_text(entity);
    }

    // Free dynamic arrays
    arrfree(manager->entities);
    arrfree(manager->entities_to_add);

    // Free entity map (including the tag arrays)
    for (size_t i = 0; i < hmlen(manager->entity_map); i++) {
        arrfree(manager->entity_map[i].value);
    }
    hmfree(manager->entity_map);

    // Free component pools
    arrfree(manager->transform_pool);
    arrfree(manager->name_pool);
    arrfree(manager->shape_pool);
    arrfree(manager->bounding_box_pool);
    arrfree(manager->text_pool);

    // Free free indices arrays
    arrfree(manager->transform_free_indices);
    arrfree(manager->name_free_indices);
    arrfree(manager->shape_free_indices);
    arrfree(manager->bounding_box_free_indices);
    arrfree(manager->text_free_indices);

    free(manager);
}

Entity* entity_manager_create_entity(EntityManager* manager, const char* tag) {
    if (!manager || !tag) {
        return NULL;
    }

    // Create new entity
    Entity new_entity = {0};
    new_entity.id = manager->next_id++;
    new_entity.alive = true;
    new_entity.manager = manager;

    // Copy tag
    strncpy(new_entity.tag, tag, sizeof(new_entity.tag) - 1);
    new_entity.tag[sizeof(new_entity.tag) - 1] = '\0';

    // Initialize component pointers to NULL
    new_entity.c_transform = NULL;
    new_entity.c_name = NULL;
    new_entity.c_shape = NULL;
    new_entity.c_bounding_box = NULL;
    new_entity.c_text = NULL;

    // Add to entities_to_add for deferred addition
    arrput(manager->entities_to_add, new_entity);

    // Return pointer to the entity in the deferred array
    return &manager->entities_to_add[arrlen(manager->entities_to_add) - 1];
}

void entity_manager_update(EntityManager* manager) {
    if (!manager) return;

    // First, add any pending entities from entities_to_add
    for (size_t i = 0; i < arrlen(manager->entities_to_add); i++) {
        Entity entity = manager->entities_to_add[i];
        entity.manager = manager; // Ensure manager pointer is correct
        arrput(manager->entities, entity);

        // Add to entity map by tag
        EntityMapEntry* entry = hmgetp_null(manager->entity_map, entity.tag);
        if (entry) {
            // Tag exists, add to existing array
            arrput(entry->value, entity);
        } else {
            // New tag, create new entry
            EntityArray new_array = NULL;
            arrput(new_array, entity);
            hmput(manager->entity_map, entity.tag, new_array);
        }
    }

    // Clear the entities_to_add array
    arrsetlen(manager->entities_to_add, 0);

    // Remove dead entities by compacting the array
    size_t write_index = 0;
    for (size_t read_index = 0; read_index < arrlen(manager->entities); read_index++) {
        Entity* entity = &manager->entities[read_index];

        if (entity->alive) {
            if (write_index != read_index) {
                // Move entity to fill the gap
                manager->entities[write_index] = *entity;
                // Update the manager pointer in the moved entity
                manager->entities[write_index].manager = manager;
            }
            write_index++;
        } else {
            // Entity is dead, remove its components
            entity_remove_transform(entity);
            entity_remove_name(entity);
            entity_remove_shape(entity);
            entity_remove_bounding_box(entity);
            entity_remove_text(entity);

            // Remove from entity map
            EntityMapEntry* entry = hmgetp_null(manager->entity_map, entity->tag);
            if (entry) {
                // Find and remove this entity from the tag array
                for (size_t j = 0; j < arrlen(entry->value); j++) {
                    if (entry->value[j].id == entity->id) {
                        arrdel(entry->value, j);
                        break;
                    }
                }
            }
        }
    }

    // Update the entities array length
    arrsetlen(manager->entities, write_index);
}

Entity* entity_manager_get_entity_by_id(EntityManager* manager, size_t id) {
    if (!manager) return NULL;

    // Search in main entities array
    for (size_t i = 0; i < arrlen(manager->entities); i++) {
        if (manager->entities[i].id == id && manager->entities[i].alive) {
            return &manager->entities[i];
        }
    }

    // Also search in entities_to_add (pending entities)
    for (size_t i = 0; i < arrlen(manager->entities_to_add); i++) {
        if (manager->entities_to_add[i].id == id && manager->entities_to_add[i].alive) {
            return &manager->entities_to_add[i];
        }
    }

    return NULL;
}

// Note: This function returns a dynamically allocated array that must be freed by the caller
Entity** entity_manager_get_entities_by_tag(EntityManager* manager, const char* tag, size_t* count) {
    if (!manager || !tag || !count) {
        if (count) *count = 0;
        return NULL;
    }

    // Use hash map for O(1) lookup
    EntityMapEntry* entry = hmgetp_null(manager->entity_map, tag);
    if (!entry || arrlen(entry->value) == 0) {
        *count = 0;
        return NULL;
    }

    // Count alive entities
    size_t alive_count = 0;
    for (size_t i = 0; i < arrlen(entry->value); i++) {
        if (entry->value[i].alive) {
            alive_count++;
        }
    }

    *count = alive_count;
    if (alive_count == 0) {
        return NULL;
    }

    // Allocate array for entity pointers
    Entity** result = malloc(sizeof(Entity*) * alive_count);
    if (!result) {
        *count = 0;
        return NULL;
    }

    // Collect alive entities
    size_t result_index = 0;
    for (size_t i = 0; i < arrlen(entry->value); i++) {
        if (entry->value[i].alive) {
            // Find the entity in the main entities array to get correct pointer
            for (size_t j = 0; j < arrlen(manager->entities); j++) {
                if (manager->entities[j].id == entry->value[i].id) {
                    result[result_index++] = &manager->entities[j];
                    break;
                }
            }
        }
    }

    return result;
}

void entity_manager_print_entities(const EntityManager* manager) {
    if (!manager) {
        printf("EntityManager is NULL\n");
        return;
    }

    printf("=== EntityManager Status ===\n");
    printf("Total entities: %zu\n", arrlen(manager->entities));
    printf("Pending entities: %zu\n", arrlen(manager->entities_to_add));
    printf("Next ID: %zu\n", manager->next_id);
    printf("\n");

    // Print main entities
    for (size_t i = 0; i < arrlen(manager->entities); i++) {
        const Entity* entity = &manager->entities[i];
        printf("Entity[%zu]: ID=%zu, Tag='%s', Alive=%s\n",
               i, entity->id, entity->tag, entity->alive ? "true" : "false");

        // Print components
        if (entity->c_transform) {
            printf("  Transform: pos(%.2f, %.2f), angle=%.2f, scale(%.2f, %.2f)\n",
                   entity->c_transform->x, entity->c_transform->y, entity->c_transform->angle,
                   entity->c_transform->scale_x, entity->c_transform->scale_y);
        }

        if (entity->c_name) {
            printf("  Name: '%s'\n", entity->c_name->name);
        }

        if (entity->c_shape) {
            printf("  Shape: type='%s'\n", entity->c_shape->type);
        }

        if (entity->c_bounding_box) {
            printf("  BoundingBox: pos(%.2f, %.2f), size(%.2f, %.2f)\n",
                   entity->c_bounding_box->x, entity->c_bounding_box->y,
                   entity->c_bounding_box->width, entity->c_bounding_box->height);
        }

        if (entity->c_text) {
            printf("  Text: '%s', font_size=%d, color(%.2f, %.2f, %.2f, %.2f)\n",
                   entity->c_text->text, entity->c_text->font_size,
                   entity->c_text->r, entity->c_text->g, entity->c_text->b, entity->c_text->a);
        }

        printf("\n");
    }

    printf("=== Component Pool Status ===\n");
    printf("Transform pool: %zu allocated, %zu free\n",
           arrlen(manager->transform_pool), arrlen(manager->transform_free_indices));
    printf("Name pool: %zu allocated, %zu free\n",
           arrlen(manager->name_pool), arrlen(manager->name_free_indices));
    printf("Shape pool: %zu allocated, %zu free\n",
           arrlen(manager->shape_pool), arrlen(manager->shape_free_indices));
    printf("BoundingBox pool: %zu allocated, %zu free\n",
           arrlen(manager->bounding_box_pool), arrlen(manager->bounding_box_free_indices));
    printf("Text pool: %zu allocated, %zu free\n",
           arrlen(manager->text_pool), arrlen(manager->text_free_indices));

    printf("=== Entity Map Status ===\n");
    printf("Tags in map: %zu\n", hmlen(manager->entity_map));
    for (size_t i = 0; i < hmlen(manager->entity_map); i++) {
        printf("  Tag '%s': %zu entities\n",
               manager->entity_map[i].key, arrlen(manager->entity_map[i].value));
    }
    printf("=============================\n\n");
}